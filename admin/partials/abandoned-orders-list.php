<?php
/**
 * صفحة قائمة الطلبات المتروكة
 */

// منع الوصول المباشر
if (!defined('ABSPATH')) {
    exit;
}

global $wpdb;

// التحقق من وجود إجراءات
$action = isset($_GET['action']) ? sanitize_text_field($_GET['action']) : '';
$order_id = isset($_GET['order_id']) ? intval($_GET['order_id']) : 0;

// معالجة الإجراءات
if ($action && $order_id && isset($_GET['_wpnonce'])) {
    if (wp_verify_nonce($_GET['_wpnonce'], 'abandoned_order_action_' . $order_id)) {
        $table_name = $wpdb->prefix . 'pexlat_form_abandoned_orders';
        
        switch ($action) {
            case 'delete':
                $wpdb->delete($table_name, array('id' => $order_id), array('%d'));
                echo '<div class="notice notice-success"><p>تم حذف الطلب بنجاح.</p></div>';
                break;
                
            case 'convert':
                // تحويل الطلب إلى ووكومرس
                $order_data = $wpdb->get_row($wpdb->prepare("SELECT * FROM {$table_name} WHERE id = %d", $order_id));
                if ($order_data) {
                    // إنشاء طلب ووكومرس جديد
                    $wc_order = wc_create_order();
                    
                    // إضافة بيانات العميل
                    $wc_order->set_billing_first_name($order_data->customer_name);
                    $wc_order->set_billing_phone($order_data->phone_number);
                    $wc_order->set_billing_email($order_data->email);
                    $wc_order->set_billing_address_1($order_data->address);
                    $wc_order->set_billing_state($order_data->wilaya);
                    $wc_order->set_billing_city($order_data->commune);
                    
                    // إضافة المنتجات
                    if ($order_data->product_data) {
                        $products = maybe_unserialize($order_data->product_data);
                        if (is_array($products)) {
                            foreach ($products as $product) {
                                if (isset($product['product_id'])) {
                                    $wc_order->add_product(wc_get_product($product['product_id']), $product['quantity'] ?? 1);
                                }
                            }
                        }
                    }
                    
                    // إضافة رسوم الشحن
                    if ($order_data->shipping_cost > 0) {
                        $shipping = new WC_Order_Item_Shipping();
                        $shipping->set_method_title('توصيل');
                        $shipping->set_total($order_data->shipping_cost);
                        $wc_order->add_item($shipping);
                    }
                    
                    $wc_order->calculate_totals();

                    // تعيين حالة الطلب إلى "قيد التنفيذ" بدلاً من "بانتظار الدفع"
                    $wc_order->set_status('processing', 'تم تحويل الطلب من الطلبات المتروكة');

                    $wc_order->save();
                    
                    // حذف الطلب من الجدول المنفصل
                    $wpdb->delete($table_name, array('id' => $order_id), array('%d'));
                    
                    echo '<div class="notice notice-success"><p>تم تحويل الطلب إلى ووكومرس بنجاح. <a href="' . admin_url('post.php?post=' . $wc_order->get_id() . '&action=edit') . '">عرض الطلب</a></p></div>';
                }
                break;
        }
    }
}

// إعدادات الصفحة
$per_page = 20;
$current_page = isset($_GET['paged']) ? max(1, intval($_GET['paged'])) : 1;
$offset = ($current_page - 1) * $per_page;

// فلاتر البحث
$search = isset($_GET['s']) ? sanitize_text_field($_GET['s']) : '';
$status_filter = isset($_GET['status']) ? sanitize_text_field($_GET['status']) : '';
$date_from = isset($_GET['date_from']) ? sanitize_text_field($_GET['date_from']) : '';
$date_to = isset($_GET['date_to']) ? sanitize_text_field($_GET['date_to']) : '';

// بناء استعلام البحث
$table_name = $wpdb->prefix . 'pexlat_form_abandoned_orders';
$where_conditions = array('1=1');
$where_values = array();

if (!empty($search)) {
    $where_conditions[] = "(customer_name LIKE %s OR phone_number LIKE %s OR email LIKE %s)";
    $search_term = '%' . $wpdb->esc_like($search) . '%';
    $where_values[] = $search_term;
    $where_values[] = $search_term;
    $where_values[] = $search_term;
}

if (!empty($status_filter)) {
    $where_conditions[] = "status = %s";
    $where_values[] = $status_filter;
}

if (!empty($date_from)) {
    $where_conditions[] = "DATE(created_at) >= %s";
    $where_values[] = $date_from;
}

if (!empty($date_to)) {
    $where_conditions[] = "DATE(created_at) <= %s";
    $where_values[] = $date_to;
}

$where_clause = implode(' AND ', $where_conditions);

// الحصول على العدد الإجمالي
$total_query = "SELECT COUNT(*) FROM {$table_name} WHERE {$where_clause}";
if (!empty($where_values)) {
    $total_items = $wpdb->get_var($wpdb->prepare($total_query, $where_values));
} else {
    $total_items = $wpdb->get_var($total_query);
}

// الحصول على البيانات
$query = "SELECT * FROM {$table_name} WHERE {$where_clause} ORDER BY created_at DESC LIMIT %d OFFSET %d";
$query_values = array_merge($where_values, array($per_page, $offset));

if (!empty($where_values)) {
    $orders = $wpdb->get_results($wpdb->prepare($query, $query_values));
} else {
    $orders = $wpdb->get_results($wpdb->prepare("SELECT * FROM {$table_name} ORDER BY created_at DESC LIMIT %d OFFSET %d", $per_page, $offset));
}

// حساب عدد الصفحات
$total_pages = ceil($total_items / $per_page);
?>

<div class="wrap">
    <h1 class="wp-heading-inline">الطلبات المتروكة</h1>
    
    <?php if (!get_option('pexlat_form_separate_abandoned_orders', 1)): ?>
        <div class="notice notice-warning">
            <p><strong>تنبيه:</strong> نظام الطلبات المتروكة المنفصل غير مفعل. يرجى تفعيله من <a href="<?php echo admin_url('admin.php?page=pexlat-form-settings#woocommerce'); ?>">إعدادات الإضافة</a>.</p>
        </div>
    <?php endif; ?>

    <!-- شريط الشعار -->
    <?php include(plugin_dir_path(__FILE__) . 'header-bar.php'); ?>

    <!-- فلاتر البحث -->
    <div class="tablenav top">
        <form method="get" action="">
            <input type="hidden" name="page" value="pexlat-form-abandoned-orders">
            
            <div class="alignleft actions">
                <input type="search" name="s" value="<?php echo esc_attr($search); ?>" placeholder="البحث في الطلبات..." class="search-input">
                
                <select name="status">
                    <option value="">جميع الحالات</option>
                    <option value="abandoned" <?php selected($status_filter, 'abandoned'); ?>>متروك</option>
                    <option value="converted" <?php selected($status_filter, 'converted'); ?>>محول</option>
                </select>
                
                <input type="date" name="date_from" value="<?php echo esc_attr($date_from); ?>" placeholder="من تاريخ">
                <input type="date" name="date_to" value="<?php echo esc_attr($date_to); ?>" placeholder="إلى تاريخ">
                
                <input type="submit" class="button" value="فلترة">
                
                <?php if ($search || $status_filter || $date_from || $date_to): ?>
                    <a href="<?php echo admin_url('admin.php?page=pexlat-form-abandoned-orders'); ?>" class="button">مسح الفلاتر</a>
                <?php endif; ?>
            </div>
        </form>
    </div>

    <!-- جدول البيانات -->
    <table class="wp-list-table widefat fixed striped">
        <thead>
            <tr>
                <th scope="col" class="manage-column">اسم العميل</th>
                <th scope="col" class="manage-column">رقم الهاتف</th>
                <th scope="col" class="manage-column">البريد الإلكتروني</th>
                <th scope="col" class="manage-column">الولاية</th>
                <th scope="col" class="manage-column">البلدية</th>
                <th scope="col" class="manage-column">المبلغ الإجمالي</th>
                <th scope="col" class="manage-column">تاريخ الإنشاء</th>
                <th scope="col" class="manage-column">الإجراءات</th>
            </tr>
        </thead>
        <tbody>
            <?php if (empty($orders)): ?>
                <tr>
                    <td colspan="8" style="text-align: center; padding: 20px;">لا توجد طلبات متروكة</td>
                </tr>
            <?php else: ?>
                <?php foreach ($orders as $order): ?>
                    <tr>
                        <td><?php echo esc_html($order->customer_name); ?></td>
                        <td><?php echo esc_html($order->phone_number); ?></td>
                        <td><?php echo esc_html($order->email); ?></td>
                        <td><?php echo esc_html($order->wilaya); ?></td>
                        <td><?php echo esc_html($order->commune); ?></td>
                        <td><?php echo esc_html($order->total_amount); ?> دج</td>
                        <td><?php echo esc_html(date('Y-m-d H:i', strtotime($order->created_at))); ?></td>
                        <td>
                            <a href="<?php echo wp_nonce_url(admin_url('admin.php?page=pexlat-form-abandoned-orders&action=convert&order_id=' . $order->id), 'abandoned_order_action_' . $order->id); ?>" 
                               class="button button-primary button-small" 
                               onclick="return confirm('هل تريد تحويل هذا الطلب إلى ووكومرس؟');">تحويل</a>
                            
                            <a href="<?php echo wp_nonce_url(admin_url('admin.php?page=pexlat-form-abandoned-orders&action=delete&order_id=' . $order->id), 'abandoned_order_action_' . $order->id); ?>" 
                               class="button button-small" 
                               onclick="return confirm('هل تريد حذف هذا الطلب نهائياً؟');">حذف</a>
                        </td>
                    </tr>
                <?php endforeach; ?>
            <?php endif; ?>
        </tbody>
    </table>

    <!-- التنقل بين الصفحات -->
    <?php if ($total_pages > 1): ?>
        <div class="tablenav bottom">
            <div class="tablenav-pages">
                <span class="displaying-num"><?php echo $total_items; ?> عنصر</span>
                <?php
                $page_links = paginate_links(array(
                    'base' => add_query_arg('paged', '%#%'),
                    'format' => '',
                    'prev_text' => '&laquo;',
                    'next_text' => '&raquo;',
                    'total' => $total_pages,
                    'current' => $current_page,
                    'type' => 'plain'
                ));
                echo $page_links;
                ?>
            </div>
        </div>
    <?php endif; ?>
</div>
