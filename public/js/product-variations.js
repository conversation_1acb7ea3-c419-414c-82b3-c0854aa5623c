/**
 * JavaScript لمعالجة متغيرات المنتج في نماذج Form Elrakami
 */
jQuery(document).ready(function($) {
    // التحقق من وجود نماذج تحتوي على منتجات متغيرة
    var $forms = $('.pexlat-form-form');

    $forms.each(function() {
        var $form = $(this);
        var isVariableProduct = $form.find('input[name="is_variable_product"]').val() === '1';

        if (isVariableProduct) {
            initVariableProduct($form);
        }
    });

    // إضافة أنماط CSS للمتغيرات حسب نوع العرض
    function addVariationStyles() {
        var $variationsContainers = $('.pexlat-form-variations');

        $variationsContainers.each(function() {
            var $container = $(this);

            // تطبيق أنماط خاصة للمتغيرات التي لها صور
            $container.find('.variation-button-label.has-image .variation-button').css({
                'background-size': 'cover',
                'background-position': 'center',
                'color': 'transparent'
            });

            // تطبيق أنماط خاصة للألوان
            $container.find('.variation-type-color .variation-button').each(function() {
                var $button = $(this);
                var colorText = $button.text().trim().toLowerCase();

                // محاولة تحويل النص إلى لون
                if (isColorName(colorText)) {
                    $button.css({
                        'background-color': colorText,
                        'color': getContrastColor(colorText)
                    });
                }
            });

            // تطبيق أنماط خاصة للمتغيرات حسب نوع العرض
            $container.find('.variation-attribute').each(function() {
                var $attribute = $(this);

                // تطبيق أنماط خاصة للمتغيرات الممتدة
                if ($attribute.hasClass('variation-display-extended')) {
                    initExtendedVariations($attribute);
                }

                // تطبيق أنماط خاصة للدوائر الملونة
                if ($attribute.hasClass('variation-display-circle') && $attribute.hasClass('variation-type-color')) {
                    initColorCircles($attribute);
                }

                // تطبيق أنماط خاصة للأزرار النصية
                if ($attribute.hasClass('variation-display-buttons')) {
                    initTextButtons($attribute);
                }

                // تطبيق أنماط خاصة للقوائم المنسدلة
                if ($attribute.hasClass('variation-display-dropdown')) {
                    initDropdowns($attribute);
                }
            });
        });
    }

    // تهيئة دوائر الألوان
    function initColorCircles($attribute) {
        // تطبيق الألوان على الدوائر
        $attribute.find('.variation-button-label.color-circle').each(function() {
            var $label = $(this);
            var $button = $label.find('.color-circle-button');
            var $input = $label.find('.variation-button-input');
            var colorName = $input.val().trim().toLowerCase();

            // تطبيق اللون تلقائيًا
            if (isColorName(colorName)) {
                // استخدام قيم الألوان المعرفة مسبقًا
                var colorValue = getColorValue(colorName);
                $button.css('background-color', colorValue);

                // تحديد لون النص المتباين
                var textColor = getContrastColor(colorName);
                $button.find('.color-name').css('color', textColor);
            }
        });

        // إضافة تأثير التحديد لدوائر الألوان
        $attribute.find('.variation-button-input').on('change', function() {
            var $input = $(this);
            var $label = $input.closest('.variation-button-label');

            // إزالة التحديد من جميع الدوائر في نفس المجموعة
            $attribute.find('.variation-button-label').removeClass('selected');

            // إضافة التحديد للدائرة المحددة
            if ($input.is(':checked')) {
                $label.addClass('selected');
            }
        });
    }

    // دالة للحصول على قيمة اللون
    function getColorValue(colorName) {
        // قائمة بالألوان وقيمها
        var colorValues = {
            'red': '#ff0000',
            'blue': '#0000ff',
            'green': '#008000',
            'yellow': '#ffff00',
            'black': '#000000',
            'white': '#ffffff',
            'orange': '#ffa500',
            'purple': '#800080',
            'pink': '#ffc0cb',
            'brown': '#a52a2a',
            'gray': '#808080',
            'grey': '#808080',
            'silver': '#c0c0c0',
            'gold': '#ffd700',
            'أحمر': '#ff0000',
            'أزرق': '#0000ff',
            'أخضر': '#008000',
            'أصفر': '#ffff00',
            'أسود': '#000000',
            'أبيض': '#ffffff',
            'برتقالي': '#ffa500',
            'بنفسجي': '#800080',
            'وردي': '#ffc0cb',
            'بني': '#a52a2a',
            'رمادي': '#808080',
            'فضي': '#c0c0c0',
            'ذهبي': '#ffd700'
        };

        // إذا كان اللون معرفًا، استخدمه، وإلا استخدم اللون نفسه
        return colorValues[colorName] || colorName;
    }

    // تهيئة المتغيرات الممتدة
    function initExtendedVariations($attribute) {
        // إضافة تأثير التحديد للمتغيرات الممتدة
        $attribute.find('.variation-button-input').on('change', function() {
            var $input = $(this);
            var $label = $input.closest('.variation-button-label');

            // إزالة التحديد من جميع المتغيرات في نفس المجموعة
            $attribute.find('.variation-button-label').removeClass('selected');

            // إضافة التحديد للمتغير المحدد
            if ($input.is(':checked')) {
                $label.addClass('selected');
            }
        });

        // تحسين عرض البطاقات الموسعة
        $attribute.find('.compact-swatch-option').each(function() {
            var $option = $(this);

            // التأكد من أن البطاقة لا تخرج عن حدود الحاوية
            $option.css({
                'max-width': '100%',
                'box-sizing': 'border-box',
                'overflow': 'hidden'
            });

            // تحسين عرض الوصف
            var $description = $option.find('.variant-description');
            if ($description.length > 0 && $description.text().length > 50) {
                $description.css({
                    'white-space': 'nowrap',
                    'overflow': 'hidden',
                    'text-overflow': 'ellipsis',
                    'max-width': '100%'
                });
            }
        });
    }

    // تهيئة الأزرار النصية
    function initTextButtons($attribute) {
        // إضافة تأثير التحديد للأزرار النصية
        $attribute.find('.variation-button-input').on('change', function() {
            var $input = $(this);
            var $label = $input.closest('.variation-button-label');

            // إزالة التحديد من جميع الأزرار في نفس المجموعة
            $attribute.find('.variation-button-label').removeClass('selected');

            // إضافة التحديد للزر المحدد
            if ($input.is(':checked')) {
                $label.addClass('selected');
            }
        });
    }

    // تهيئة القوائم المنسدلة
    function initDropdowns($attribute) {
        // معالجة تغيير القائمة المنسدلة
        $attribute.find('.variation-dropdown').on('change', function() {
            var $dropdown = $(this);
            var attributeName = $dropdown.attr('name');
            var selectedValue = $dropdown.val();

            // تحديث قيمة السمة المحددة
            if (selectedValue) {
                // تشغيل حدث التغيير لتحديث معلومات المتغير
                $dropdown.trigger('variation_selected', [attributeName, selectedValue]);
            }
        });

        // إضافة مستمع للحدث المخصص
        $attribute.find('.variation-dropdown').on('variation_selected', function(event, attributeName, selectedValue) {
            // البحث عن المتغير المطابق وتحديث المعلومات
            var $form = $(this).closest('.pexlat-form-form');
            $form.find('.variation-button-input').trigger('change');
        });
    }

    // دالة للتحقق مما إذا كان النص يمثل اسم لون
    function isColorName(text) {
        // قائمة بأسماء الألوان الشائعة
        var commonColors = [
            'red', 'blue', 'green', 'yellow', 'black', 'white', 'orange',
            'purple', 'pink', 'brown', 'gray', 'grey', 'silver', 'gold',
            'أحمر', 'أزرق', 'أخضر', 'أصفر', 'أسود', 'أبيض', 'برتقالي',
            'بنفسجي', 'وردي', 'بني', 'رمادي', 'فضي', 'ذهبي'
        ];

        // التحقق من وجود النص في قائمة الألوان
        return commonColors.indexOf(text) !== -1 || text.startsWith('#') || /^rgb\(/.test(text);
    }

    // دالة لحساب لون النص المتباين مع لون الخلفية
    function getContrastColor(bgColor) {
        // تحويل اسم اللون إلى قيم RGB
        var colors = {
            'red': [255, 0, 0],
            'blue': [0, 0, 255],
            'green': [0, 128, 0],
            'yellow': [255, 255, 0],
            'black': [0, 0, 0],
            'white': [255, 255, 255],
            'orange': [255, 165, 0],
            'purple': [128, 0, 128],
            'pink': [255, 192, 203],
            'brown': [165, 42, 42],
            'gray': [128, 128, 128],
            'grey': [128, 128, 128],
            'silver': [192, 192, 192],
            'gold': [255, 215, 0],
            'أحمر': [255, 0, 0],
            'أزرق': [0, 0, 255],
            'أخضر': [0, 128, 0],
            'أصفر': [255, 255, 0],
            'أسود': [0, 0, 0],
            'أبيض': [255, 255, 255],
            'برتقالي': [255, 165, 0],
            'بنفسجي': [128, 0, 128],
            'وردي': [255, 192, 203],
            'بني': [165, 42, 42],
            'رمادي': [128, 128, 128],
            'فضي': [192, 192, 192],
            'ذهبي': [255, 215, 0]
        };

        var rgb = colors[bgColor] || [128, 128, 128]; // قيمة افتراضية

        // حساب درجة السطوع
        var brightness = (rgb[0] * 299 + rgb[1] * 587 + rgb[2] * 114) / 1000;

        // إذا كان اللون فاتحًا، استخدم نصًا داكنًا، وإلا استخدم نصًا فاتحًا
        return brightness > 128 ? '#000000' : '#ffffff';
    }

    // تطبيق أنماط المتغيرات
    addVariationStyles();

    /**
     * تهيئة وظائف المنتج المتغير
     *
     * @param {jQuery} $form النموذج الذي يحتوي على منتج متغير
     */
    function initVariableProduct($form) {
        var productId = $form.find('input[name="product_id"]').val();
        var $variationButtons = $form.find('.variation-button-input');
        var $variationPriceInfo = $form.find('.variation-price-info');
        var $variationPrice = $form.find('.variation-price');
        var $variationAvailability = $form.find('.variation-availability');
        var $productPriceInput = $form.find('input[name="product_price"]');
        var $variationIdInput = $form.find('input[name="variation_id"]');
        var $originalPriceInput = $form.find('input[name="original_price"]');
        var originalPrice = parseFloat($originalPriceInput.val()) || 0;

        // الحصول على إعدادات عرض السعر
        var priceDisplayType = 'range'; // القيمة الافتراضية

        // استدعاء AJAX لجلب إعدادات عرض السعر
        $.ajax({
            url: formElrakami.ajaxurl,
            type: 'POST',
            data: {
                action: 'pexlat_form_get_product_settings',
                product_id: productId,
                nonce: formElrakami.nonce
            },
            dataType: 'json',
            success: function(response) {
                if (response.success && response.data) {
                    priceDisplayType = response.data.price_display_type || 'range';
                }
            },
            error: function(xhr, status, error) {
                // خطأ في تحميل إعدادات عرض السعر
            }
        });

        // التحقق من حالة توفر المنتج الأصلي
        var isProductInStock = $form.find('input[name="product_in_stock"]').val() || '1';
        $form.data('product-in-stock', isProductInStock);

        // تحديث حالة زر الطلب بناءً على توفر المنتج الأصلي
        var $submitButton = $form.find('button[type="submit"]');
        if (isProductInStock !== '1') {
            $submitButton.prop('disabled', true).addClass('disabled');
        }

        // تحديد المتغير الافتراضي تلقائيًا
        selectDefaultVariation($form);

        // معالجة تغيير الكمية يدويًا
        var $quantityInput = $form.find('input[name="quantity"]');
        $quantityInput.on('change', function() {
            var newQuantity = parseInt($(this).val()) || 1;
            var requiredQuantity = $form.data('selected-variation-required-quantity');
            var $selectedInput = $form.data('selected-variation-input');

            // تحديث عرض الكمية في ملخص الطلب
            $('.product-quantity-badge .quantity-number').text(newQuantity);

            // جمع جميع المتغيرات مع كمياتها المطلوبة
            var variationsWithQuantities = [];
            $form.find('.variation-button-input').each(function() {
                var $input = $(this);
                var inputRequiredQuantity = parseInt($input.data('required-quantity')) || 0;
                if (inputRequiredQuantity > 0) {
                    variationsWithQuantities.push({
                        input: $input,
                        quantity: inputRequiredQuantity
                    });
                }
            });

            // ترتيب المتغيرات حسب الكمية (من الأصغر إلى الأكبر)
            variationsWithQuantities.sort(function(a, b) {
                return a.quantity - b.quantity;
            });



            // التحقق مما إذا كان هناك متغير محدد حاليًا
            if ($selectedInput) {
                var currentRequiredQuantity = parseInt($selectedInput.data('required-quantity')) || 0;


                // إذا كانت الكمية الجديدة أقل من الكمية المطلوبة للمتغير المحدد حاليًا
                if (newQuantity < currentRequiredQuantity) {

                    // البحث عن المتغير المناسب للكمية الجديدة
                    var bestMatch = null;

                    // البحث عن أكبر كمية مطلوبة أقل من أو تساوي الكمية الجديدة
                    for (var i = variationsWithQuantities.length - 1; i >= 0; i--) {
                        var variation = variationsWithQuantities[i];
                        if (variation.quantity <= newQuantity) {
                            bestMatch = variation;
                            break;
                        }
                    }



                    // إذا وجدنا متغيرًا مناسبًا
                    if (bestMatch) {
                        // إذا كان المتغير المحدد حاليًا مختلفًا عن المتغير المناسب
                        if ($selectedInput[0] !== bestMatch.input[0]) {

                            // إلغاء تحديد المتغير الحالي
                            $selectedInput.prop('checked', false);

                            // تحديد المتغير الجديد
                            bestMatch.input.prop('checked', true).trigger('change');
                        }
                    } else {
                        // إذا لم نجد متغيرًا مناسبًا، نحتفظ بالكمية الجديدة ونلغي تحديد المتغير الحالي
                        $selectedInput.prop('checked', false);
                        $form.removeData('selected-variation-required-quantity');
                        $form.removeData('selected-variation-input');
                        updateTotalPrice($form);
                    }
                } else {
                    // الكمية الجديدة أكبر من أو تساوي الكمية المطلوبة للمتغير المحدد حاليًا
                    // نتحقق مما إذا كان هناك متغير آخر أكثر ملاءمة
                    var betterMatch = null;

                    // البحث عن أكبر كمية مطلوبة أقل من أو تساوي الكمية الجديدة
                    for (var i = variationsWithQuantities.length - 1; i >= 0; i--) {
                        var variation = variationsWithQuantities[i];
                        if (variation.quantity <= newQuantity && variation.quantity > currentRequiredQuantity) {
                            betterMatch = variation;
                            break;
                        }
                    }

                    // إذا وجدنا متغيرًا أفضل
                    if (betterMatch) {

                        // إلغاء تحديد المتغير الحالي
                        $selectedInput.prop('checked', false);

                        // تحديد المتغير الجديد
                        betterMatch.input.prop('checked', true).trigger('change');
                    } else {
                        // لا يوجد متغير أفضل، نحتفظ بالمتغير الحالي
                        updateTotalPrice($form);
                    }
                }
            } else {
                // لا يوجد متغير محدد حاليًا

                // البحث عن المتغير المناسب للكمية الجديدة
                var bestMatch = null;

                // البحث عن أكبر كمية مطلوبة أقل من أو تساوي الكمية الجديدة
                for (var i = variationsWithQuantities.length - 1; i >= 0; i--) {
                    var variation = variationsWithQuantities[i];
                    if (variation.quantity <= newQuantity) {
                        bestMatch = variation;
                        break;
                    }
                }



                // إذا وجدنا متغيرًا مناسبًا
                if (bestMatch) {

                    // تحديد المتغير المناسب
                    bestMatch.input.prop('checked', true).trigger('change');
                } else if (variationsWithQuantities.length > 0) {
                    // إذا لم نجد متغيرًا مناسبًا، نختار المتغير الأول
                    var firstVariation = variationsWithQuantities[0];

                    // تحديد المتغير الأول
                    firstVariation.input.prop('checked', true).trigger('change');
                } else {
                    // لا توجد متغيرات، نحدث السعر الإجمالي فقط
                    updateTotalPrice($form);
                }
            }
        });

        // تحميل بيانات المتغيرات من WooCommerce
        var variationsData = [];

        // استدعاء AJAX لجلب بيانات المتغيرات
        $.ajax({
            url: formElrakami.ajaxurl,
            type: 'POST',
            data: {
                action: 'pexlat_form_get_variations',
                product_id: productId,
                nonce: formElrakami.nonce
            },
            dataType: 'json',
            success: function(response) {
                if (response.success && response.data) {
                    variationsData = response.data;
                }
            },
            error: function(xhr, status, error) {
                // خطأ في تحميل بيانات المتغيرات
            }
        });

        // معالجة تغيير أزرار المتغيرات وقوائم المتغيرات
        $form.on('change', '.variation-button-input, .variation-dropdown', function() {
            var $input = $(this);
            var $quantityInput = $form.find('input[name="quantity"]');
            var currentQuantity = parseInt($quantityInput.val()) || 1;

            // التحقق مما إذا كان المتغير المحدد يحتوي على كمية مطلوبة
            if ($input.is(':checked') && $input.data('required-quantity')) {
                var requiredQuantity = parseInt($input.data('required-quantity'));
                if (requiredQuantity > 0) {

                    // تحديث الكمية إلى الكمية المطلوبة دائمًا عند تغيير المتغير
                    $quantityInput.val(requiredQuantity);
                    // تحديث الكمية فقط في ملخص الطلب وليس في بطاقات العروض
                    $('.product-quantity-badge .quantity-number').text(requiredQuantity);

                    // تخزين الكمية المطلوبة للمتغير المحدد
                    $form.data('selected-variation-required-quantity', requiredQuantity);
                    $form.data('selected-variation-input', $input);
                }
            } else {
                // إذا تم إلغاء تحديد المتغير، نحتفظ بالكمية الحالية
                if ($form.data('selected-variation-input') === $input) {
                    $form.removeData('selected-variation-required-quantity');
                    $form.removeData('selected-variation-input');
                }
            }

            // تحديث عرض الكمية في ملخص الطلب عند تغيير المتغير
            var quantity = parseInt($form.find('input[name="quantity"]').val()) || 1;
            // تحديث الكمية فقط في ملخص الطلب وليس في بطاقات العروض
            $('.product-quantity-badge .quantity-number').text(quantity);

            var selectedAttributes = {};
            var allSelected = true;

            // جمع السمات المحددة من أزرار الراديو
            $form.find('.variation-button-input:checked').each(function() {
                var attributeName = $(this).attr('name');
                var attributeValue = $(this).val();

                if (attributeValue) {
                    selectedAttributes[attributeName] = attributeValue;
                } else {
                    allSelected = false;
                }
            });

            // جمع السمات المحددة من القوائم المنسدلة
            $form.find('.variation-dropdown').each(function() {
                var attributeName = $(this).attr('name');
                var attributeValue = $(this).val();

                if (attributeValue) {
                    selectedAttributes[attributeName] = attributeValue;
                } else {
                    allSelected = false;
                }
            });

            // إذا تم تحديد جميع السمات، ابحث عن المتغير المطابق
            if (Object.keys(selectedAttributes).length > 0 && variationsData.length > 0) {
                var matchedVariation = findMatchingVariation(variationsData, selectedAttributes);

                if (matchedVariation) {
                    // تحديث معلومات المتغير
                    updateVariationInfo(matchedVariation);
                } else {
                    // لم يتم العثور على متغير مطابق
                    resetVariationInfo();
                    $variationAvailability.text('هذا المتغير غير متوفر').addClass('out-of-stock');
                }
            } else {
                // إعادة تعيين معلومات المتغير إذا لم يتم تحديد جميع السمات
                resetVariationInfo();
            }
        });

        /**
         * البحث عن المتغير المطابق للسمات المحددة
         *
         * @param {Array} variations مصفوفة المتغيرات
         * @param {Object} attributes السمات المحددة
         * @return {Object|null} المتغير المطابق أو null إذا لم يتم العثور عليه
         */
        function findMatchingVariation(variations, attributes) {
            for (var i = 0; i < variations.length; i++) {
                var variation = variations[i];
                var isMatch = true;

                // التحقق من تطابق جميع السمات المحددة
                for (var attrName in attributes) {
                    if (variation.attributes[attrName] !== attributes[attrName]) {
                        isMatch = false;
                        break;
                    }
                }

                if (isMatch) {
                    return variation;
                }
            }

            return null;
        }

        /**
         * تحديث معلومات المتغير في النموذج
         *
         * @param {Object} variation المتغير المطابق
         */
        function updateVariationInfo(variation) {
            // تحديث السعر
            var displayPrice = parseFloat(variation.display_price) || 0;

            // تحديث عرض السعر
            $variationPrice.text(formatPrice(displayPrice));

            // الحصول على زر الطلب
            var $submitButton = $form.find('button[type="submit"]');

            // تحديث حالة المخزون
            if (variation.is_in_stock) {
                var inStockText = 'متوفر في المخزون';
                // استخدام الترجمة إذا كانت متوفرة
                if (typeof formElrakami !== 'undefined' && formElrakami.translations && formElrakami.translations['متوفر في المخزون']) {
                    inStockText = formElrakami.translations['متوفر في المخزون'];
                }
                $variationAvailability.text(inStockText).removeClass('out-of-stock').addClass('in-stock');

                // تفعيل زر الطلب
                $submitButton.prop('disabled', false).removeClass('disabled');

                // تفعيل أزرار السلة
                updateCartButtonsState($form, true);
            } else {
                var outOfStockText = 'غير متوفر في المخزون';
                // استخدام الترجمة إذا كانت متوفرة
                if (typeof formElrakami !== 'undefined' && formElrakami.translations && formElrakami.translations['غير متوفر في المخزون']) {
                    outOfStockText = formElrakami.translations['غير متوفر في المخزون'];
                }
                $variationAvailability.text(outOfStockText).removeClass('in-stock').addClass('out-of-stock');

                // تعطيل زر الطلب
                $submitButton.prop('disabled', true).addClass('disabled');

                // تعطيل أزرار السلة
                updateCartButtonsState($form, false);
            }

            // تحديث معرف المتغير وسعر المنتج في النموذج
            $variationIdInput.val(variation.variation_id);
            $productPriceInput.val(displayPrice);

            // تحديث عرض الكمية في ملخص الطلب
            var quantity = parseInt($form.find('input[name="quantity"]').val()) || 1;
            // تحديث الكمية فقط في ملخص الطلب وليس في بطاقات العروض
            $('.product-quantity-badge .quantity-number').text(quantity);

            // تحديث السعر الإجمالي
            updateTotalPrice($form);
        }

        /**
         * إعادة تعيين معلومات المتغير
         */
        function resetVariationInfo() {
            $variationPrice.empty();
            $variationAvailability.empty().removeClass('in-stock out-of-stock');
            $variationIdInput.val(0);

            // الحصول على زر الطلب
            var $submitButton = $form.find('button[type="submit"]');

            // التحقق من توفر المنتج الأصلي
            var isOriginalProductInStock = $form.data('product-in-stock');

            // تحديث حالة زر الطلب بناءً على توفر المنتج الأصلي
            if (isOriginalProductInStock === '1') {
                $submitButton.prop('disabled', false).removeClass('disabled');
                // تفعيل أزرار السلة
                updateCartButtonsState($form, true);
            } else {
                $submitButton.prop('disabled', true).addClass('disabled');
                // تعطيل أزرار السلة
                updateCartButtonsState($form, false);
            }

            // استعادة السعر الأصلي للمنتج
            $productPriceInput.val(originalPrice);

            // تحديث عرض الكمية في ملخص الطلب
            var quantity = parseInt($form.find('input[name="quantity"]').val()) || 1;
            // تحديث الكمية فقط في ملخص الطلب وليس في بطاقات العروض
            $('.product-quantity-badge .quantity-number').text(quantity);

            updateTotalPrice($form);
        }
    }

    /**
     * تنسيق السعر مع العملة
     *
     * @param {number} price السعر المراد تنسيقه
     * @return {string} السعر المنسق
     */
    function formatPrice(price) {
        // استخدام إعدادات WooCommerce للعملة إذا كانت متوفرة من formElrakami
        if (typeof formElrakami !== 'undefined' && formElrakami.currency_symbol) {
            var symbol = formElrakami.currency_symbol;
            var position = formElrakami.currency_position || 'left';
            var decimals = parseInt(formElrakami.price_decimals) || 2;
            var decimal_sep = formElrakami.price_decimal_separator || '.';
            var thousand_sep = formElrakami.price_thousand_separator || ',';

            var formatted_price = price.toFixed(decimals);
            if (decimal_sep !== '.') {
                formatted_price = formatted_price.replace('.', decimal_sep);
            }
            if (thousand_sep) {
                formatted_price = formatted_price.replace(/\B(?=(\d{3})+(?!\d))/g, thousand_sep);
            }

            switch (position) {
                case 'left':
                    return symbol + formatted_price;
                case 'right':
                    return formatted_price + symbol;
                case 'left_space':
                    return symbol + ' ' + formatted_price;
                case 'right_space':
                    return formatted_price + ' ' + symbol;
                default:
                    return symbol + formatted_price;
            }
        }

        // استخدام إعدادات WooCommerce للعملة إذا كانت متوفرة من woocommerce_params
        if (typeof woocommerce_params !== 'undefined' && woocommerce_params.currency_format_symbol) {
            var symbol = woocommerce_params.currency_format_symbol;
            var position = woocommerce_params.currency_format_symbol_pos || 'left';
            var decimals = parseInt(woocommerce_params.currency_format_num_decimals) || 2;
            var decimal_sep = woocommerce_params.currency_format_decimal_sep || '.';
            var thousand_sep = woocommerce_params.currency_format_thousand_sep || ',';

            var formatted_price = price.toFixed(decimals);
            if (decimal_sep !== '.') {
                formatted_price = formatted_price.replace('.', decimal_sep);
            }
            if (thousand_sep) {
                formatted_price = formatted_price.replace(/\B(?=(\d{3})+(?!\d))/g, thousand_sep);
            }

            switch (position) {
                case 'left':
                    return symbol + formatted_price;
                case 'right':
                    return formatted_price + symbol;
                case 'left_space':
                    return symbol + ' ' + formatted_price;
                case 'right_space':
                    return formatted_price + ' ' + symbol;
                default:
                    return symbol + formatted_price;
            }
        }

        // كحل أخير، استخدم تنسيق افتراضي
        return price.toFixed(2) + ' د.ج';
    }

    /**
     * تحديث السعر الإجمالي في النموذج
     *
     * @param {jQuery} $form النموذج المراد تحديث السعر فيه
     */
    function updateTotalPrice($form) {
        var $totalDisplay = $form.find('.total-price-display');
        var $productPriceDisplay = $form.find('.product-price-display');
        var $shippingPriceDisplay = $form.find('.shipping-price-display');
        var $quantityInput = $form.find('input[name="quantity"]');

        if ($totalDisplay.length > 0) {
            // الحصول على سعر المنتج الحالي
            var basePrice = parseFloat($form.find('input[name="product_price"]').val()) || 0;

            // الحصول على الكمية
            var quantity = parseInt($quantityInput.val()) || 1;

            // تحديث عرض الكمية في ملخص الطلب
            // تحديث الكمية فقط في ملخص الطلب وليس في بطاقات العروض
            $('.product-quantity-badge .quantity-number').text(quantity);

            // حساب سعر المنتج الإجمالي
            var productPrice = basePrice * quantity;

            // الحصول على تكلفة الشحن
            var shippingCost = parseFloat($form.find('input[name="shipping_cost"]').val()) || 0;

            // حساب السعر الإجمالي
            var totalPrice = productPrice + shippingCost;

            // تحديث العرض
            if ($productPriceDisplay.length > 0) {
                $productPriceDisplay.text(formatPrice(productPrice));
            }

            if ($shippingPriceDisplay.length > 0) {
                // التحقق من حالة اختيار الولاية
                var $stateField = $form.find('select[name="state"]');

                // التحقق مما إذا كان حقل الولاية موجودًا ومفعلًا
                if ($stateField.length > 0 && $stateField.is(':visible')) {
                    // التحقق مما إذا لم يتم اختيار قيمة للولاية
                    if (!$stateField.val() || $stateField.val() === '') {
                        // عرض "اختر الولاية" إذا لم يتم اختيار الولاية
                        $shippingPriceDisplay.html('<span class="select-state-message">اختر الولاية</span>');
                        return; // الخروج من الدالة لمنع تحديث سعر الشحن
                    }
                }

                // إذا تم اختيار الولاية أو كان حقل الولاية غير موجود
                if (shippingCost === 0) {
                    // عرض "توصيل مجاني" في بطاقة خضراء إذا كان سعر الشحن صفر
                    var freeShippingText = formElrakami.translations && formElrakami.translations.free_shipping || 'توصيل مجاني';
                    $shippingPriceDisplay.html('<span class="free-shipping-badge">' + freeShippingText + '</span>');
                } else {
                    // عرض سعر الشحن بالطريقة العادية
                    $shippingPriceDisplay.text(formatPrice(shippingCost));
                }
            }

            $totalDisplay.text(formatPrice(totalPrice));

            // تحديث السعر في زر الطلب إذا كان مفعلاً
            var $buttonTotalPrice = $form.find('.pexlat-form-submit .button-total-price .total-price-display');
            if ($buttonTotalPrice.length > 0) {
                $buttonTotalPrice.text(formatPrice(totalPrice));
            }

            // تحديث السعر في الزر المثبت إذا كان مفعلاً
            var $stickyButtonTotalPrice = $('.pexlat-form-sticky-bar-button .sticky-button-total-price .total-price-display');
            if ($stickyButtonTotalPrice.length > 0) {
                $stickyButtonTotalPrice.text(formatPrice(totalPrice));
            }
        }
    }

    /**
     * تحديد المتغير الافتراضي تلقائيًا
     *
     * @param {jQuery} $form النموذج المراد تحديد المتغير الافتراضي فيه
     */
    function selectDefaultVariation($form) {

        // الحصول على الكمية الافتراضية
        var $quantityInput = $form.find('input[name="quantity"]');
        var defaultQuantity = parseInt($quantityInput.val()) || 1;

        // جمع جميع المتغيرات مع كمياتها المطلوبة
        var variationsWithQuantities = [];
        $form.find('.variation-button-input').each(function() {
            var $input = $(this);
            var inputRequiredQuantity = parseInt($input.data('required-quantity')) || 0;
            if (inputRequiredQuantity > 0) {
                variationsWithQuantities.push({
                    input: $input,
                    quantity: inputRequiredQuantity
                });
            }
        });

        // إذا لم تكن هناك متغيرات مع كميات مطلوبة، نختار المتغير الأول
        if (variationsWithQuantities.length === 0) {
            var $firstVariation = $form.find('.variation-button-input').first();
            if ($firstVariation.length > 0) {
                $firstVariation.prop('checked', true).trigger('change');
            }
            return;
        }

        // ترتيب المتغيرات حسب الكمية (من الأصغر إلى الأكبر)
        variationsWithQuantities.sort(function(a, b) {
            return a.quantity - b.quantity;
        });



        // البحث عن المتغير المناسب للكمية الافتراضية
        var bestMatch = null;

        // البحث عن أكبر كمية مطلوبة أقل من أو تساوي الكمية الافتراضية
        for (var i = variationsWithQuantities.length - 1; i >= 0; i--) {
            var variation = variationsWithQuantities[i];
            if (variation.quantity <= defaultQuantity) {
                bestMatch = variation;
                break;
            }
        }

        // إذا وجدنا متغيرًا مناسبًا
        if (bestMatch) {

            // تحديد المتغير المناسب
            bestMatch.input.prop('checked', true).trigger('change');
        } else {
            // إذا لم نجد متغيرًا مناسبًا، نختار المتغير الأول
            var firstVariation = variationsWithQuantities[0];

            // تحديث الكمية إلى الكمية المطلوبة للمتغير الأول
            $quantityInput.val(firstVariation.quantity);
            $('.product-quantity-badge .quantity-number').text(firstVariation.quantity);

            // تحديد المتغير الأول
            firstVariation.input.prop('checked', true).trigger('change');
        }
    }

    /**
     * تحديث حالة أزرار السلة بناءً على توفر المنتج
     */
    function updateCartButtonsState($form, isInStock) {
        // تحديث زر السلة المنفصل
        var $cartButton = $form.find('.pexlat-form-add-to-cart');
        if ($cartButton.length > 0) {
            if (isInStock) {
                $cartButton.prop('disabled', false).removeClass('disabled');
                // استعادة النص الأصلي
                var originalText = $cartButton.data('original-text') || 'أضف إلى السلة';
                $cartButton.find('.out-of-stock-message').remove();
                if ($cartButton.find('.button-icon').length > 0) {
                    $cartButton.find('.button-icon').after(' ' + originalText);
                } else {
                    $cartButton.text(originalText);
                }
            } else {
                $cartButton.prop('disabled', true).addClass('disabled');
                // إضافة رسالة عدم التوفر
                $cartButton.find('.out-of-stock-message').remove();
                $cartButton.append('<span class="out-of-stock-message">المنتج غير متوفر حالياً</span>');
            }
        }

        // تحديث أيقونة السلة المدمجة
        var $cartIcon = $form.find('.cart-icon-inline');
        if ($cartIcon.length > 0) {
            if (isInStock) {
                $cartIcon.show().css('opacity', '1').css('cursor', 'pointer');
            } else {
                $cartIcon.hide();
            }
        }
    }
});
