/**
 * أنماط صفحة الطلبات المتروكة
 */

/* تنسيق عام للصفحة */
.wrap h1.wp-heading-inline {
    color: #1d2327;
    font-size: 23px;
    font-weight: 400;
    margin: 0 0 8px 0;
    padding: 9px 0 4px 0;
    line-height: 1.3;
}

/* تنسيق شريط الفلاتر */
.tablenav.top {
    background: #f8f9fa;
    border: 1px solid #e2e4e7;
    border-radius: 6px;
    padding: 15px;
    margin: 20px 0;
}

.tablenav.top .alignleft.actions {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    align-items: center;
}

.tablenav.top input[type="search"],
.tablenav.top input[type="date"],
.tablenav.top select {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    min-width: 150px;
}

.tablenav.top input[type="search"] {
    min-width: 200px;
}

.tablenav.top .button {
    padding: 8px 16px;
    font-size: 14px;
    border-radius: 4px;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 5px;
}

.tablenav.top .button:hover {
    background-color: #f0f0f1;
}

/* تنسيق الجدول */
.wp-list-table {
    border: 1px solid #e2e4e7;
    border-radius: 6px;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.wp-list-table thead th {
    background: #f8f9fa;
    border-bottom: 1px solid #e2e4e7;
    padding: 12px 15px;
    font-weight: 600;
    color: #1d2327;
    text-align: right;
}

.wp-list-table tbody td {
    padding: 12px 15px;
    border-bottom: 1px solid #f0f0f1;
    vertical-align: middle;
}

.wp-list-table tbody tr:hover {
    background-color: #f8f9fa;
}

.wp-list-table tbody tr:last-child td {
    border-bottom: none;
}

/* تنسيق الأزرار في الجدول */
.wp-list-table .button-small {
    padding: 6px 12px;
    font-size: 12px;
    line-height: 1.4;
    margin-left: 5px;
    border-radius: 3px;
}

.wp-list-table .button-primary {
    background: #2271b1;
    border-color: #2271b1;
    color: #fff;
}

.wp-list-table .button-primary:hover {
    background: #135e96;
    border-color: #135e96;
}

/* تنسيق رسائل التنبيه */
.notice {
    border-radius: 6px;
    margin: 20px 0;
    padding: 12px 15px;
}

.notice.notice-warning {
    border-right: 4px solid #dba617;
    background: #fcf9e8;
}

.notice.notice-success {
    border-right: 4px solid #00a32a;
    background: #edfaef;
}

.notice p {
    margin: 0;
    font-size: 14px;
}

.notice a {
    color: #2271b1;
    text-decoration: none;
}

.notice a:hover {
    text-decoration: underline;
}

/* تنسيق التنقل بين الصفحات */
.tablenav.bottom {
    margin-top: 20px;
    padding: 15px 0;
    border-top: 1px solid #e2e4e7;
}

.tablenav-pages {
    text-align: center;
}

.tablenav-pages .displaying-num {
    color: #646970;
    font-size: 14px;
    margin-left: 15px;
}

.tablenav-pages .page-numbers {
    display: inline-block;
    padding: 8px 12px;
    margin: 0 2px;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    color: #2271b1;
    font-size: 14px;
}

.tablenav-pages .page-numbers:hover {
    background: #f0f0f1;
}

.tablenav-pages .page-numbers.current {
    background: #2271b1;
    color: #fff;
    border-color: #2271b1;
}

.tablenav-pages .page-numbers.prev,
.tablenav-pages .page-numbers.next {
    font-weight: bold;
}

/* تنسيق الحالة الفارغة */
.wp-list-table tbody td[colspan] {
    text-align: center;
    color: #646970;
    font-style: italic;
    padding: 40px 20px;
}

/* تنسيق متجاوب */
@media screen and (max-width: 782px) {
    .tablenav.top .alignleft.actions {
        flex-direction: column;
        align-items: stretch;
    }
    
    .tablenav.top input[type="search"],
    .tablenav.top input[type="date"],
    .tablenav.top select {
        min-width: auto;
        width: 100%;
        margin-bottom: 10px;
    }
    
    .wp-list-table {
        font-size: 14px;
    }
    
    .wp-list-table thead th,
    .wp-list-table tbody td {
        padding: 8px 10px;
    }
    
    .wp-list-table .button-small {
        padding: 4px 8px;
        font-size: 11px;
        margin: 2px;
        display: block;
        text-align: center;
    }
}

/* تحسينات إضافية */
.wp-list-table tbody tr:nth-child(even) {
    background-color: #fafafa;
}

.wp-list-table tbody tr:nth-child(even):hover {
    background-color: #f0f0f1;
}

/* تنسيق الأيقونات */
.dashicons {
    vertical-align: middle;
    margin-left: 5px;
}

/* تنسيق النصوص */
.wp-list-table tbody td {
    font-size: 14px;
    line-height: 1.4;
}

/* تنسيق الروابط */
.wp-list-table a {
    text-decoration: none;
}

.wp-list-table a:hover {
    text-decoration: underline;
}

/* تنسيق المبالغ */
.wp-list-table .amount {
    font-weight: 600;
    color: #2271b1;
}

/* تنسيق التواريخ */
.wp-list-table .date {
    color: #646970;
    font-size: 13px;
}

/* تنسيق الحالات */
.status-badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
}

.status-badge.abandoned {
    background: #fcf2e8;
    color: #b32d2e;
    border: 1px solid #f1aeb5;
}

.status-badge.converted {
    background: #edfaef;
    color: #00a32a;
    border: 1px solid #68de7c;
}
