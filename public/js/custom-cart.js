/**
 * JavaScript للسلة المخصصة
 * Form Elrakami Custom Cart System
 */

jQuery(document).ready(function($) {
    'use strict';

    // متغيرات عامة
    const $cartSidebar = $('#cart-sidebar');
    const $floatingIcon = $('#floating-cart-icon');
    const $cartCount = $('.cart-count');
    const $cartItemsContainer = $('.cart-items-container');

    /**
     * تجديد رمز الأمان للسلة المخصصة
     */
    function refreshCartNonce(callback) {
        // التحقق من توفر المتغير العام
        var ajaxUrl = (typeof formElrakami !== 'undefined' && formElrakami.ajaxurl) ?
                      formElrakami.ajaxurl :
                      (typeof ajaxurl !== 'undefined' ? ajaxurl : '/wp-admin/admin-ajax.php');

        $.ajax({
            url: ajaxUrl,
            type: 'POST',
            data: {
                action: 'pexlat_form_refresh_nonce'
            },
            success: function(response) {
                if (response.success && response.data.nonce) {
                    // تحديث رمز الأمان في المتغير العام
                    if (typeof formElrakami !== 'undefined') {
                        formElrakami.nonce = response.data.nonce;
                    }

                    console.log('تم تجديد رمز الأمان للسلة بنجاح');
                    callback(true);
                } else {
                    console.error('فشل في تجديد رمز الأمان للسلة');
                    callback(false);
                }
            },
            error: function() {
                console.error('خطأ في طلب تجديد رمز الأمان للسلة');
                callback(false);
            }
        });
    }

    // تهيئة السلة
    initCart();

    /**
     * تهيئة السلة
     */
    function initCart() {
        // تحديث عداد السلة عند تحميل الصفحة
        updateCartDisplay();

        // ربط الأحداث
        bindEvents();

        // التحقق من حالة السلة وتحديث الفورم بعد تأخير قصير
        setTimeout(checkCartStatusAndUpdateForm, 500);
    }

    /**
     * ربط الأحداث
     */
    function bindEvents() {
        // فتح السلة عند النقر على الأيقونة العائمة
        $floatingIcon.on('click', openCartSidebar);

        // إغلاق السلة
        $cartSidebar.on('click', '.cart-close-btn, .cart-sidebar-overlay', closeCartSidebar);

        // منع إغلاق السلة عند النقر على المحتوى
        $cartSidebar.on('click', '.cart-sidebar-content', function(e) {
            e.stopPropagation();
        });

        // إضافة منتج إلى السلة
        $(document).on('click', '.pexlat-form-add-to-cart', handleAddToCart);

        // إضافة منتج إلى السلة من الأيقونة المدمجة
        $(document).on('click', '.cart-icon-inline', handleAddToCartInline);

        // تحديث الكمية
        $cartSidebar.on('click', '.qty-btn', handleQuantityChange);
        $cartSidebar.on('change', '.qty-input', handleQuantityInput);

        // حذف منتج من السلة
        $cartSidebar.on('click', '.remove-item', handleRemoveItem);

        // إتمام الطلب
        $cartSidebar.on('click', '.btn-checkout', handleCheckout);

        // متابعة التسوق
        $cartSidebar.on('click', '.btn-continue-shopping', closeCartSidebar);



        // منع إرسال النموذج عند النقر على زر السلة
        $(document).on('click', '.pexlat-form-add-to-cart', function(e) {
            e.preventDefault();
            e.stopPropagation();
        });
    }

    /**
     * فتح السلة الجانبية
     */
    function openCartSidebar() {
        $cartSidebar.addClass('active');
        $('body').addClass('cart-sidebar-open');

        // تحديث محتويات السلة
        updateCartContents();
    }

    /**
     * إغلاق السلة الجانبية
     */
    function closeCartSidebar() {
        $cartSidebar.removeClass('active');
        $('body').removeClass('cart-sidebar-open');
    }

    /**
     * معالجة إضافة منتج إلى السلة
     */
    function handleAddToCart(e) {
        e.preventDefault();

        const $button = $(this);
        const $form = $button.closest('.pexlat-form-form');

        // التحقق من أن الزر غير معطل (المنتج متوفر)
        if ($button.prop('disabled') || $button.hasClass('disabled')) {
            showNotification('هذا المنتج غير متوفر حالياً', 'error');
            return;
        }

        // التحقق من صحة النموذج
        if (!validateForm($form)) {
            showNotification('يرجى ملء جميع الحقول المطلوبة', 'error');
            return;
        }

        // تعطيل الزر مؤقتاً
        $button.prop('disabled', true).text('جاري الإضافة...');

        // جمع بيانات النموذج
        const formData = collectFormData($form);
        
        // إضافة بيانات AJAX
        formData.append('action', 'add_to_custom_cart');
        formData.append('nonce', customCart.nonce);

        // إرسال الطلب
        $.ajax({
            url: customCart.ajaxurl,
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                if (response.success) {
                    showNotification(customCart.texts.added_to_cart, 'success');
                    updateCartCount(response.data.cart_count);

                    // تحديث حالة الفورم بعد إضافة المنتج
                    updateFormBasedOnCartStatus(false);

                    // فتح السلة تلقائياً
                    setTimeout(openCartSidebar, 500);
                } else {
                    showNotification(response.data || customCart.texts.error_occurred, 'error');
                }
            },
            error: function(xhr, status, error) {
                // التحقق من أخطاء الأمان
                if (xhr.status === 403 || (xhr.responseText &&
                    (xhr.responseText.includes('رمز الأمان') || xhr.responseText.includes('nonce')))) {

                    console.log('تم اكتشاف خطأ في رمز الأمان للسلة، محاولة التجديد...');

                    // محاولة تجديد رمز الأمان وإعادة المحاولة
                    refreshCartNonce(function(success) {
                        if (success) {
                            // إعادة محاولة إضافة المنتج
                            setTimeout(function() {
                                $button.trigger('click');
                            }, 500);
                        } else {
                            showNotification('خطأ في رمز الأمان. يرجى تحديث الصفحة والمحاولة مرة أخرى.', 'error');
                        }
                    });
                } else {
                    showNotification(customCart.texts.error_occurred, 'error');
                }
            },
            complete: function() {
                // إعادة تفعيل الزر
                const originalText = $button.data('original-text') || 'أضف إلى السلة';
                $button.prop('disabled', false).text(originalText);
            }
        });
    }

    /**
     * معالجة إضافة منتج إلى السلة من الأيقونة المدمجة
     */
    function handleAddToCartInline(e) {
        e.preventDefault();
        e.stopPropagation();

        const $icon = $(this);
        const $form = $icon.closest('.pexlat-form-form');

        // التحقق من توفر المنتج (فحص زر الطلب الرئيسي)
        const $submitButton = $form.find('button[type="submit"]');
        if ($submitButton.prop('disabled') || $submitButton.hasClass('disabled')) {
            showNotification('هذا المنتج غير متوفر حالياً', 'error');
            return;
        }

        // التحقق من صحة النموذج
        if (!validateForm($form)) {
            showNotification('يرجى ملء جميع الحقول المطلوبة', 'error');
            return;
        }

        // إضافة كلاس التحميل للأيقونة
        $icon.addClass('loading');

        // جمع بيانات النموذج
        const formData = collectFormData($form);

        // إضافة بيانات AJAX
        formData.append('action', 'add_to_custom_cart');
        formData.append('nonce', customCart.nonce);

        // إرسال الطلب
        $.ajax({
            url: customCart.ajaxurl,
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                if (response.success) {
                    showNotification(customCart.texts.added_to_cart, 'success');
                    updateCartCount(response.data.cart_count);

                    // تحديث حالة الفورم بعد إضافة المنتج
                    updateFormBasedOnCartStatus(false);

                    // فتح السلة تلقائياً
                    setTimeout(openCartSidebar, 500);
                } else {
                    showNotification(response.data || customCart.texts.error_occurred, 'error');
                }
            },
            error: function() {
                showNotification(customCart.texts.error_occurred, 'error');
            },
            complete: function() {
                // إزالة كلاس التحميل
                $icon.removeClass('loading');
            }
        });
    }

    /**
     * جمع بيانات النموذج
     */
    function collectFormData($form) {
        const formData = new FormData();

        // جمع جميع حقول النموذج
        $form.find('input, select, textarea').each(function() {
            const $field = $(this);
            const name = $field.attr('name');
            const value = $field.val();

            if (name && value) {
                if ($field.attr('type') === 'checkbox' || $field.attr('type') === 'radio') {
                    if ($field.is(':checked')) {
                        formData.append(name, value);
                    }
                } else {
                    formData.append(name, value);
                }
            }
        });

        return formData;
    }

    /**
     * التحقق من صحة النموذج
     */
    function validateForm($form) {
        let isValid = true;

        // التحقق من الحقول المطلوبة المرئية فقط
        $form.find('[required]').each(function() {
            const $field = $(this);
            const $fieldContainer = $field.closest('.pexlat-form-fields, .pexlat-form-address-fields, .shipping-methods-container');

            // تجاهل الحقول المخفية
            if ($fieldContainer.length > 0 && !$fieldContainer.is(':visible')) {
                return true; // تخطي هذا الحقل
            }

            if (!$field.val() || $field.val().trim() === '') {
                $field.addClass('error');
                isValid = false;
            } else {
                $field.removeClass('error');
            }
        });

        return isValid;
    }

    /**
     * معالجة تغيير الكمية
     */
    function handleQuantityChange(e) {
        e.preventDefault();
        
        const $button = $(this);
        const $cartItem = $button.closest('.cart-item');
        const $qtyInput = $cartItem.find('.qty-input');
        const productId = $cartItem.data('product-id');
        const isPlus = $button.hasClass('qty-plus');
        
        let currentQty = parseInt($qtyInput.val()) || 1;
        let newQty = isPlus ? currentQty + 1 : Math.max(1, currentQty - 1);
        
        updateProductQuantity(productId, newQty, $qtyInput);
    }

    /**
     * معالجة إدخال الكمية مباشرة
     */
    function handleQuantityInput(e) {
        const $input = $(this);
        const $cartItem = $input.closest('.cart-item');
        const productId = $cartItem.data('product-id');
        const newQty = Math.max(1, parseInt($input.val()) || 1);
        
        updateProductQuantity(productId, newQty, $input);
    }

    /**
     * تحديث كمية المنتج
     */
    function updateProductQuantity(productId, quantity, $input) {
        $.ajax({
            url: customCart.ajaxurl,
            type: 'POST',
            data: {
                action: 'update_cart_quantity',
                nonce: customCart.nonce,
                product_id: productId,
                quantity: quantity
            },
            success: function(response) {
                if (response.success) {
                    $input.val(quantity);
                    updateCartDisplay();
                    showNotification(customCart.texts.cart_updated, 'success');
                    // تحديث حالة الفورم (السلة ما زالت تحتوي على منتجات)
                    updateFormBasedOnCartStatus(false);
                } else {
                    showNotification(response.data || customCart.texts.error_occurred, 'error');
                }
            },
            error: function() {
                showNotification(customCart.texts.error_occurred, 'error');
            }
        });
    }

    /**
     * معالجة حذف منتج
     */
    function handleRemoveItem(e) {
        e.preventDefault();
        
        const $cartItem = $(this).closest('.cart-item');
        const productId = $cartItem.data('product-id');
        
        if (!confirm('هل أنت متأكد من حذف هذا المنتج من السلة؟')) {
            return;
        }

        $.ajax({
            url: customCart.ajaxurl,
            type: 'POST',
            data: {
                action: 'remove_from_custom_cart',
                nonce: customCart.nonce,
                product_id: productId
            },
            success: function(response) {
                if (response.success) {
                    $cartItem.fadeOut(300, function() {
                        $(this).remove();
                        updateCartDisplay();
                        // تحديث حالة الفورم بعد حذف المنتج
                        setTimeout(function() {
                            updateFormBasedOnCartStatus();
                        }, 500);
                    });
                    showNotification(customCart.texts.removed_from_cart, 'success');
                } else {
                    showNotification(response.data || customCart.texts.error_occurred, 'error');
                }
            },
            error: function() {
                showNotification(customCart.texts.error_occurred, 'error');
            }
        });
    }

    /**
     * معالجة إتمام الطلب
     */
    function handleCheckout(e) {
        e.preventDefault();

        const $button = $(this);
        $button.prop('disabled', true).text('جاري المعالجة...');

        // جمع بيانات طريقة الدفع المختارة
        const $selectedPaymentMethod = $('input[name="payment_method"]:checked');
        const paymentMethod = $selectedPaymentMethod.length > 0 ? $selectedPaymentMethod.val() : '';

        $.ajax({
            url: customCart.ajaxurl,
            type: 'POST',
            data: {
                action: 'complete_cart_order',
                nonce: customCart.nonce,
                payment_method: paymentMethod
            },
            success: function(response) {
                console.log('استجابة إتمام الطلب:', response); // للتصحيح
                console.log('نوع الاستجابة:', typeof response); // للتصحيح

                // التحقق من أن الاستجابة هي كائن JSON صحيح
                if (typeof response === 'string') {
                    try {
                        response = JSON.parse(response);
                    } catch (e) {
                        console.error('خطأ في تحليل JSON:', e);
                        console.error('محتوى الاستجابة:', response);
                        showNotification('حدث خطأ في معالجة الاستجابة. يرجى المحاولة مرة أخرى.', 'error');
                        return;
                    }
                }

                if (response && response.success) {
                    showNotification(response.data.message, 'success');

                    // إعادة تعيين السلة
                    updateCartCount(0);
                    closeCartSidebar();

                    // تحديث حالة الفورم بعد إتمام الطلب (السلة فارغة)
                    updateFormBasedOnCartStatus(true);

                    // التعامل مع طرق الدفع المختلفة
                    if (response.data.needs_payment === true) {
                        // تسجيل معلومات التشخيص
                        console.log('Payment redirect info:', {
                            order_id: response.data.order_id,
                            redirect_url: response.data.redirect_url,
                            payment_method: response.data.payment_method,
                            payment_method_title: response.data.payment_method_title
                        });

                        // إذا كان الطلب يحتاج دفع، نوجه فوراً إلى صفحة الدفع
                        if (response.data.redirect_url) {
                            setTimeout(function() {
                                console.log('Redirecting to payment page:', response.data.redirect_url);
                                window.location.href = response.data.redirect_url;
                            }, 1500); // وقت أطول قليلاً لقراءة الرسالة
                        } else {
                            console.error('No redirect URL provided for payment');
                        }
                    } else {
                        // إعادة توجيه إلى صفحة الطلب إذا كان متاحاً
                        if (response.data && response.data.redirect_url) {
                            console.log('إعادة التوجيه إلى:', response.data.redirect_url); // للتصحيح
                            setTimeout(function() {
                                window.location.href = response.data.redirect_url;
                            }, 1500);
                        } else {
                            console.log('لا يوجد رابط إعادة توجيه في الاستجابة'); // للتصحيح
                        }
                    }
                } else {
                    console.log('فشل في إتمام الطلب:', response); // للتصحيح
                    var errorMessage = 'حدث خطأ، يرجى المحاولة مرة أخرى';

                    if (response && response.data) {
                        if (typeof response.data === 'string') {
                            errorMessage = response.data;
                        } else if (response.data.message) {
                            errorMessage = response.data.message;
                        }
                    }

                    showNotification(errorMessage, 'error');
                }
            },
            error: function(xhr, status, error) {
                console.log('خطأ AJAX:', xhr, status, error); // للتصحيح
                console.log('حالة الاستجابة:', xhr.status); // للتصحيح
                console.log('نص الاستجابة:', xhr.responseText); // للتصحيح

                var errorMessage = 'حدث خطأ، يرجى المحاولة مرة أخرى';

                // محاولة استخراج رسالة خطأ أكثر تفصيلاً
                if (xhr.responseText) {
                    try {
                        var response = JSON.parse(xhr.responseText);
                        if (response && response.data && response.data.message) {
                            errorMessage = response.data.message;
                        }
                    } catch (e) {
                        // تجاهل أخطاء تحليل JSON
                    }
                }

                showNotification(errorMessage, 'error');
            },
            complete: function() {
                $button.prop('disabled', false).text(customCart.texts.checkout);
                console.log('انتهت عملية إتمام الطلب'); // للتصحيح
            }
        });
    }

    /**
     * تحديث عرض السلة
     */
    function updateCartDisplay() {
        updateCartContents();
    }

    /**
     * تحديث محتويات السلة
     */
    function updateCartContents() {
        $.ajax({
            url: customCart.ajaxurl,
            type: 'POST',
            data: {
                action: 'get_cart_contents',
                nonce: customCart.nonce
            },
            success: function(response) {
                if (response.success) {
                    const data = response.data;

                    // تحديث HTML السلة
                    $cartItemsContainer.html(data.cart_html);

                    // تحديث المجاميع
                    $('.subtotal-amount').text(formatPrice(data.subtotal));
                    $('.shipping-amount').text(formatPrice(data.shipping));
                    $('.total-amount').text(formatPrice(data.total));

                    // تحديث العداد
                    updateCartCount(data.cart_count);



                    // إظهار/إخفاء أزرار الإجراءات
                    if (data.empty) {
                        $('.cart-actions').hide();
                    } else {
                        $('.cart-actions').show();
                    }

                    // تحديث حالة الفورم بناءً على حالة السلة
                    updateFormBasedOnCartStatus(data.empty);

                    // إظهار رسالة تحذير إذا تم حذف منتجات
                    if (data.warning) {
                        showNotification(data.warning, 'warning');
                    }
                } else {
                    console.error('خطأ في تحديث محتويات السلة:', response.data);
                }
            },
            error: function(xhr, status, error) {
                console.error('خطأ في AJAX:', error);
                showNotification('خطأ في تحديث السلة', 'error');
            }
        });
    }

    /**
     * تحديث عداد السلة
     */
    function updateCartCount(count) {
        $cartCount.text(count);
        
        if (count > 0) {
            $cartCount.removeClass('hidden');
        } else {
            $cartCount.addClass('hidden');
        }
    }

    /**
     * تنسيق السعر
     */
    function formatPrice(price) {
        return parseFloat(price).toLocaleString('ar-DZ', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        }) + ' ' + customCart.currency;
    }

    /**
     * إظهار إشعار
     */
    function showNotification(message, type) {
        // إنشاء عنصر الإشعار إذا لم يكن موجوداً
        let $notification = $('.cart-notification');
        if ($notification.length === 0) {
            $notification = $('<div class="cart-notification"></div>');
            $('body').append($notification);
        }

        // تعيين نوع ومحتوى الإشعار
        $notification.removeClass('success error info warning').addClass(type);
        $notification.text(message);
        $notification.fadeIn();

        // إخفاء الإشعار بعد 3 ثوانٍ
        setTimeout(function() {
            $notification.fadeOut();
        }, 3000);
    }

    /**
     * التحقق من حالة السلة وتحديث الفورم
     */
    function checkCartStatusAndUpdateForm() {
        $.ajax({
            url: customCart.ajaxurl,
            type: 'POST',
            data: {
                action: 'get_cart_contents',
                nonce: customCart.nonce
            },
            success: function(response) {
                if (response.success) {
                    const data = response.data;
                    updateFormBasedOnCartStatus(data.empty);

                    // إظهار رسالة تحذير إذا تم حذف منتجات
                    if (data.warning) {
                        showNotification(data.warning, 'warning');
                    }
                }
            },
            error: function() {
                console.log('خطأ في التحقق من حالة السلة');
            }
        });
    }

    /**
     * تحديث الفورم بناءً على حالة السلة
     */
    function updateFormBasedOnCartStatus(isEmpty = null) {
        // إذا لم يتم تمرير حالة السلة، نحصل عليها من العداد
        if (isEmpty === null) {
            const cartCount = parseInt($cartCount.text()) || 0;
            isEmpty = cartCount === 0;
        }

        // العثور على جميع النماذج في الصفحة
        $('.pexlat-form-form').each(function() {
            const $form = $(this);

            if (isEmpty) {
                // السلة فارغة - إظهار جميع الحقول
                $form.removeClass('cart-has-items');
                $form.find('.pexlat-form-fields').show();
                $form.find('.pexlat-form-address-fields').show();
                $form.find('.shipping-methods-container').show();
                $form.find('.cart-items-message').remove();
            } else {
                // السلة تحتوي على منتجات - إخفاء حقول المعلومات الشخصية وطرق التوصيل
                $form.addClass('cart-has-items');
                $form.find('.pexlat-form-fields').hide();
                $form.find('.pexlat-form-address-fields').hide();
                $form.find('.shipping-methods-container').hide();

                // إظهار رسالة توضيحية
                showCartItemsMessage($form);
            }
        });
    }

    /**
     * إظهار رسالة توضيحية عند وجود منتجات في السلة
     */
    function showCartItemsMessage($form) {
        // إزالة الرسالة السابقة إن وجدت
        $form.find('.cart-items-message').remove();

        // الحصول على عدد المنتجات في السلة
        const cartCount = parseInt($cartCount.text()) || 0;
        const cartText = cartCount === 1 ? 'منتج واحد' : cartCount + ' منتجات';

        // إضافة رسالة جديدة
        const message = `
            <div class="cart-items-message">
                <div class="cart-message-content">
                    <i class="fas fa-shopping-cart"></i>
                    <p>لديك ${cartText} في السلة بالفعل</p>
                    <small>تم إخفاء حقول المعلومات الشخصية لأنها محفوظة من الطلب السابق.<br>يمكنك إضافة هذا المنتج مباشرة أو مراجعة السلة.</small>
                    <div class="cart-message-actions">
                        <button type="button" class="btn-view-cart">
                            <i class="fas fa-eye"></i> عرض السلة
                        </button>
                    </div>
                </div>
            </div>
        `;

        // إدراج الرسالة في بداية النموذج
        $form.prepend(message);

        // ربط حدث النقر على زر عرض السلة
        $form.find('.btn-view-cart').on('click', function() {
            openCartSidebar();
        });
    }

    // إضافة أنماط CSS للإشعارات والرسائل
    if ($('.cart-notification').length === 0) {
        $('head').append(`
            <style>
            .cart-notification {
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 12px 20px;
                border-radius: 6px;
                color: white;
                font-weight: 500;
                z-index: 10001;
                display: none;
                max-width: 300px;
                word-wrap: break-word;
            }
            .cart-notification.success { background: #28a745; }
            .cart-notification.error { background: #dc3545; }
            .cart-notification.info { background: #17a2b8; }
            .cart-notification.warning { background: #ffc107; color: #212529; }
            body.cart-sidebar-open { overflow: hidden; }

            /* أنماط رسالة السلة */
            .cart-items-message {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                padding: 20px;
                border-radius: 12px;
                margin-bottom: 20px;
                text-align: center;
                box-shadow: 0 4px 15px rgba(0,0,0,0.1);
                animation: slideInDown 0.5s ease-out;
            }

            @keyframes slideInDown {
                from {
                    opacity: 0;
                    transform: translateY(-20px);
                }
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }

            .cart-message-content i {
                font-size: 2em;
                margin-bottom: 10px;
                display: block;
                animation: bounce 2s infinite;
            }

            @keyframes bounce {
                0%, 20%, 50%, 80%, 100% {
                    transform: translateY(0);
                }
                40% {
                    transform: translateY(-10px);
                }
                60% {
                    transform: translateY(-5px);
                }
            }

            .cart-message-content p {
                font-size: 1.1em;
                font-weight: 600;
                margin: 10px 0 5px 0;
            }

            .cart-message-content small {
                opacity: 0.9;
                display: block;
                margin-bottom: 15px;
                line-height: 1.4;
            }

            .cart-message-actions {
                display: flex;
                gap: 10px;
                justify-content: center;
                flex-wrap: wrap;
            }

            .btn-view-cart {
                background: rgba(255,255,255,0.2);
                border: 2px solid rgba(255,255,255,0.3);
                color: white;
                padding: 10px 20px;
                border-radius: 25px;
                cursor: pointer;
                transition: all 0.3s ease;
                font-size: 0.9em;
                text-decoration: none;
                display: inline-flex;
                align-items: center;
                gap: 8px;
            }

            .btn-view-cart:hover {
                background: rgba(255,255,255,0.3);
                border-color: rgba(255,255,255,0.5);
                transform: translateY(-2px);
                color: white;
                box-shadow: 0 4px 8px rgba(0,0,0,0.2);
            }

            /* إخفاء الحقول عند وجود منتجات في السلة */
            .pexlat-form-form.cart-has-items .pexlat-form-fields,
            .pexlat-form-form.cart-has-items .pexlat-form-address-fields,
            .pexlat-form-form.cart-has-items .shipping-methods-container {
                display: none !important;
            }

            /* تحسينات للشاشات الصغيرة */
            @media (max-width: 768px) {
                .cart-items-message {
                    margin: 10px;
                    padding: 15px;
                }

                .btn-view-cart {
                    width: 100%;
                    justify-content: center;
                }
            }
            </style>
        `);
    }
});
